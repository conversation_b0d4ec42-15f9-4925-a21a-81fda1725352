@echo off
REM Java Modules Demo - Windows 运行脚本
REM 这个脚本提供了多种方式来构建和运行 Java 模块化示例项目

setlocal enabledelayedexpansion

REM 检查 Java 版本
:check_java
echo [INFO] 检查 Java 版本...
java -version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Java 未安装或不在 PATH 中
    exit /b 1
)

for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%g
    set JAVA_VERSION=!JAVA_VERSION:"=!
    for /f "delims=." %%a in ("!JAVA_VERSION!") do set JAVA_MAJOR=%%a
)

if !JAVA_MAJOR! LSS 21 (
    echo [ERROR] 需要 Java 21 或更高版本，当前版本: !JAVA_MAJOR!
    exit /b 1
)

echo [SUCCESS] Java 版本检查通过
goto :eof

REM 检查 Maven 版本
:check_maven
echo [INFO] 检查 Maven 版本...
mvn -version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Maven 未安装或不在 PATH 中
    exit /b 1
)
echo [SUCCESS] Maven 版本检查通过
goto :eof

REM 清理项目
:clean_project
echo [INFO] 清理项目...
mvn clean
if exist target\modules rmdir /s /q target\modules
if exist logs rmdir /s /q logs
echo [SUCCESS] 项目清理完成
goto :eof

REM 编译项目
:compile_project
echo [INFO] 编译项目...
mvn compile
if errorlevel 1 (
    echo [ERROR] 编译失败
    exit /b 1
)
echo [SUCCESS] 项目编译完成
goto :eof

REM 运行测试
:run_tests
echo [INFO] 运行单元测试...
mvn test
if errorlevel 1 (
    echo [ERROR] 测试失败
    exit /b 1
)
echo [SUCCESS] 单元测试完成
goto :eof

REM 运行集成测试
:run_integration_tests
echo [INFO] 运行集成测试...
mvn verify
if errorlevel 1 (
    echo [ERROR] 集成测试失败
    exit /b 1
)
echo [SUCCESS] 集成测试完成
goto :eof

REM 打包项目
:package_project
echo [INFO] 打包项目...
mvn package
if errorlevel 1 (
    echo [ERROR] 打包失败
    exit /b 1
)

REM 创建模块目录
if not exist target\modules mkdir target\modules

REM 复制模块 JAR
for /r %%i in (target\*.jar) do (
    echo %%i | findstr /v "integration-tests" >nul
    if not errorlevel 1 (
        copy "%%i" target\modules\ >nul
    )
)

REM 复制依赖 JAR
mvn dependency:copy-dependencies -DoutputDirectory=target/modules -DincludeScope=runtime

echo [SUCCESS] 项目打包完成，模块 JAR 位于 target\modules\
goto :eof

REM 运行应用程序
:run_application
echo [INFO] 运行客户端应用程序...

if not exist target\modules (
    echo [WARNING] 项目未打包，正在打包...
    call :package_project
)

REM 创建日志目录
if not exist logs mkdir logs

REM 运行应用程序
java --module-path target\modules --module com.example.jpms.client/com.example.jpms.client.ClientApplication
goto :eof

REM 运行应用程序（开发模式）
:run_application_dev
echo [INFO] 运行客户端应用程序（开发模式）...
mvn -pl client-app exec:java -Dexec.mainClass="com.example.jpms.client.ClientApplication"
goto :eof

REM 显示模块信息
:show_module_info
echo [INFO] 显示模块信息...

if not exist target\modules (
    echo [WARNING] 项目未打包，正在打包...
    call :package_project
)

echo.
echo [INFO] === 模块列表 ===
for %%f in (target\modules\*.jar) do (
    set filename=%%~nf
    echo !filename! | findstr /c:"core-utils" /c:"user-service" /c:"client-app" >nul
    if not errorlevel 1 (
        echo   %%~nxf
        java --module-path target\modules --describe-module !filename! 2>nul
        echo.
    )
)
goto :eof

REM 显示帮助信息
:show_help
echo Java Modules Demo - Windows 运行脚本
echo.
echo 用法: %0 [命令]
echo.
echo 可用命令:
echo   check       - 检查环境（Java 和 Maven 版本）
echo   clean       - 清理项目
echo   compile     - 编译项目
echo   test        - 运行单元测试
echo   integration - 运行集成测试
echo   package     - 打包项目
echo   run         - 运行客户端应用程序
echo   run-dev     - 运行客户端应用程序（开发模式）
echo   modules     - 显示模块信息
echo   all         - 执行完整的构建和测试流程
echo   help        - 显示此帮助信息
echo.
echo 示例:
echo   %0 all      # 完整构建和测试
echo   %0 run      # 运行应用程序
goto :eof

REM 执行完整流程
:run_all
echo [INFO] 执行完整的构建和测试流程...

call :check_java
call :check_maven
call :clean_project
call :compile_project
call :run_tests
call :run_integration_tests
call :package_project
call :show_module_info

echo [SUCCESS] 完整流程执行完成！
echo [INFO] 现在可以运行: %0 run
goto :eof

REM 主函数
if "%1"=="check" (
    call :check_java
    call :check_maven
) else if "%1"=="clean" (
    call :clean_project
) else if "%1"=="compile" (
    call :compile_project
) else if "%1"=="test" (
    call :run_tests
) else if "%1"=="integration" (
    call :run_integration_tests
) else if "%1"=="package" (
    call :package_project
) else if "%1"=="run" (
    call :run_application
) else if "%1"=="run-dev" (
    call :run_application_dev
) else if "%1"=="modules" (
    call :show_module_info
) else if "%1"=="all" (
    call :run_all
) else (
    call :show_help
)
