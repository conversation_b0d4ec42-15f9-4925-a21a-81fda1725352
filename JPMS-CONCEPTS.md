# Java 平台模块系统 (JPMS) 核心概念详解

本文档详细解释了本示例项目中展示的 JPMS 核心概念和特性。

## 📋 目录

1. [模块系统基础](#模块系统基础)
2. [module-info.java 详解](#module-infojava-详解)
3. [模块依赖关系](#模块依赖关系)
4. [包的导出和封装](#包的导出和封装)
5. [服务提供者接口 (SPI)](#服务提供者接口-spi)
6. [反射访问控制](#反射访问控制)
7. [模块路径 vs 类路径](#模块路径-vs-类路径)
8. [常见问题和解决方案](#常见问题和解决方案)

## 模块系统基础

### 什么是模块？

模块是 Java 9 引入的一个新概念，它是一个命名的、自描述的代码和数据集合。每个模块：

- 有一个唯一的名称
- 明确声明其依赖关系
- 明确声明其导出的 API
- 可以控制反射访问

### 模块的优势

1. **强封装**: 只有明确导出的包才能被其他模块访问
2. **可靠配置**: 在编译时和启动时检查依赖关系
3. **可扩展性**: 支持服务提供者接口模式
4. **安全性**: 更好的访问控制

## module-info.java 详解

每个模块的根目录下都必须有一个 `module-info.java` 文件，这是模块的描述符。

### 基本语法

```java
module 模块名称 {
    // 模块声明
}
```

### 核心指令

#### 1. requires - 声明依赖

```java
// 基本依赖
requires java.base;  // 隐式依赖，通常不需要显式声明

// 外部模块依赖
requires com.example.jpms.core;

// 传递依赖 - 依赖此模块的其他模块也能访问传递的依赖
requires transitive org.slf4j;

// 静态依赖 - 编译时需要，运行时可选
requires static lombok;
```

**示例解释**:
- `requires com.example.jpms.core`: 当前模块依赖 core 模块
- `requires transitive org.slf4j`: 依赖当前模块的其他模块也能使用 SLF4J

#### 2. exports - 导出包

```java
// 导出包给所有模块
exports com.example.jpms.core.util;

// 导出包给特定模块
exports com.example.jpms.core.internal to com.example.jpms.userservice;
```

**重要**: 只有导出的包才能被其他模块访问。

#### 3. opens - 开放包用于反射

```java
// 开放包给所有模块进行反射访问
opens com.example.jpms.core.model;

// 开放包给特定模块进行反射访问
opens com.example.jpms.core.model to com.fasterxml.jackson.databind;
```

**用途**: 主要用于与需要反射访问的框架集成，如 Jackson、Hibernate 等。

#### 4. uses - 声明服务消费

```java
uses com.example.jpms.core.spi.DataProcessor;
```

**作用**: 声明当前模块使用某个服务接口。

#### 5. provides - 提供服务实现

```java
provides com.example.jpms.core.spi.DataProcessor 
    with com.example.jpms.core.internal.DefaultDataProcessor;
```

**作用**: 声明当前模块提供某个服务接口的实现。

## 模块依赖关系

### 依赖类型

1. **直接依赖**: 模块直接声明的依赖
2. **传递依赖**: 通过其他模块间接获得的依赖
3. **隐式依赖**: 所有模块都隐式依赖 `java.base`

### 本项目的依赖关系

```
client-app
    └── user-service
        └── core-utils
            ├── org.slf4j (transitive)
            ├── jackson-core
            └── jackson-databind
```

### 传递依赖示例

在 `core-utils` 模块中：
```java
requires transitive org.slf4j;
```

这意味着依赖 `core-utils` 的模块（如 `user-service`）也能直接使用 SLF4J，无需显式声明依赖。

## 包的导出和封装

### 导出规则

1. **默认封装**: 模块中的所有包默认都是封装的（不可访问）
2. **显式导出**: 只有通过 `exports` 声明的包才能被其他模块访问
3. **限定导出**: 可以限制只对特定模块导出

### 示例

在 `core-utils` 模块中：

```java
module com.example.jpms.core {
    // 这些包可以被所有模块访问
    exports com.example.jpms.core.util;
    exports com.example.jpms.core.model;
    exports com.example.jpms.core.spi;
    
    // 这个包是内部的，不能被其他模块访问
    // com.example.jpms.core.internal (未导出)
}
```

### 封装的好处

1. **API 清晰**: 明确区分公共 API 和内部实现
2. **向后兼容**: 内部实现可以自由修改而不影响其他模块
3. **安全性**: 防止意外访问内部实现

## 服务提供者接口 (SPI)

SPI 是一种设计模式，允许在运行时发现和加载服务实现。

### SPI 的组成部分

1. **服务接口**: 定义服务的契约
2. **服务提供者**: 实现服务接口的类
3. **服务消费者**: 使用服务的代码
4. **ServiceLoader**: 用于加载服务实现的工具类

### 本项目的 SPI 示例

#### 1. 定义服务接口

```java
// 在 core-utils 模块中
public interface DataProcessor {
    String getName();
    String process(String data) throws ProcessingException;
    boolean canProcess(String data);
    int getPriority();
}
```

#### 2. 声明服务使用和提供

```java
// 在 core-utils 的 module-info.java 中
module com.example.jpms.core {
    // 声明使用服务
    uses com.example.jpms.core.spi.DataProcessor;
    
    // 提供默认实现
    provides com.example.jpms.core.spi.DataProcessor 
        with com.example.jpms.core.internal.DefaultDataProcessor;
}

// 在 user-service 的 module-info.java 中
module com.example.jpms.userservice {
    // 使用服务
    uses com.example.jpms.core.spi.DataProcessor;
    
    // 提供额外实现
    provides com.example.jpms.core.spi.DataProcessor 
        with com.example.jpms.userservice.internal.JsonDataProcessor,
             com.example.jpms.userservice.internal.XmlDataProcessor;
}
```

#### 3. 加载和使用服务

```java
// 在服务消费者中
List<DataProcessor> processors = ServiceLoader.load(DataProcessor.class)
        .stream()
        .map(ServiceLoader.Provider::get)
        .sorted((p1, p2) -> Integer.compare(p2.getPriority(), p1.getPriority()))
        .collect(Collectors.toList());
```

### SPI 的优势

1. **松耦合**: 服务消费者不需要知道具体实现
2. **可扩展性**: 可以在运行时添加新的服务实现
3. **模块化**: 不同模块可以提供不同的服务实现

## 反射访问控制

### 问题背景

模块系统默认禁止对非导出包的反射访问，这可能会影响一些依赖反射的框架。

### 解决方案

使用 `opens` 指令开放包进行反射访问：

```java
// 开放给所有模块
opens com.example.jpms.core.model;

// 开放给特定模块（推荐）
opens com.example.jpms.core.model to com.fasterxml.jackson.databind;
```

### 本项目示例

```java
// 在 core-utils 模块中
opens com.example.jpms.core.model to com.fasterxml.jackson.databind;

// 在 user-service 模块中
opens com.example.jpms.userservice.dto to com.fasterxml.jackson.databind;
```

这允许 Jackson 库对模型类进行反射访问以实现 JSON 序列化/反序列化。

## 模块路径 vs 类路径

### 类路径 (Classpath)

- Java 9 之前的传统方式
- 所有 JAR 文件放在一个扁平的命名空间中
- 没有封装，所有公共类都可以访问
- 可能出现"JAR 地狱"问题

### 模块路径 (Module Path)

- Java 9 引入的新方式
- 每个 JAR 文件是一个独立的模块
- 强封装，只有导出的包才能访问
- 编译时和运行时依赖检查

### 混合使用

可以同时使用模块路径和类路径：

```bash
java --module-path modules --class-path libs --module myapp/com.example.Main
```

### 自动模块

放在模块路径上的非模块化 JAR 会被视为"自动模块"：

- 模块名称从 JAR 文件名推导
- 导出所有包
- 可以访问类路径上的所有类

## 常见问题和解决方案

### 1. 模块找不到

**错误**: `Module not found`

**原因**: 
- 模块不在模块路径上
- 模块名称不匹配

**解决方案**:
- 检查模块路径配置
- 验证 `module-info.java` 中的模块名称

### 2. 包不可访问

**错误**: `Package is not visible`

**原因**: 包没有被导出

**解决方案**:
- 在 `module-info.java` 中添加 `exports` 声明
- 或者使用 `opens` 进行反射访问

### 3. 服务加载失败

**错误**: `ServiceLoader` 找不到实现

**原因**: 
- 缺少 `provides` 声明
- 缺少 `uses` 声明

**解决方案**:
- 在提供服务的模块中添加 `provides` 声明
- 在使用服务的模块中添加 `uses` 声明

### 4. 反射访问被拒绝

**错误**: `Unable to make field accessible`

**原因**: 模块系统阻止了反射访问

**解决方案**:
- 使用 `opens` 指令开放包
- 或者在运行时使用 `--add-opens` 参数

### 5. 循环依赖

**错误**: `Cyclic dependence`

**原因**: 模块间存在循环依赖

**解决方案**:
- 重新设计模块结构
- 提取共同依赖到单独的模块
- 使用服务接口解耦

---

通过理解这些核心概念，您可以更好地设计和实现模块化的 Java 应用程序。本示例项目展示了这些概念的实际应用，建议结合代码进行学习。
