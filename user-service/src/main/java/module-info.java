/**
 * User service module demonstrating module dependencies and service providers.
 * 
 * This module demonstrates:
 * - Module dependencies (requires)
 * - Transitive dependencies
 * - Service provider implementations
 * - Package exports for service APIs
 */
module com.example.jpms.userservice {
    // Dependency on core utilities module
    requires com.example.jpms.core;
    
    // Jackson dependencies for JSON processing
    requires com.fasterxml.jackson.core;
    requires com.fasterxml.jackson.databind;
    
    // Export service API packages
    exports com.example.jpms.userservice.api;
    exports com.example.jpms.userservice.dto;
    
    // Open DTO packages for Jackson serialization
    opens com.example.jpms.userservice.dto to com.fasterxml.jackson.databind;
    
    // Use the DataProcessor service from core module
    uses com.example.jpms.core.spi.DataProcessor;
    
    // Provide additional DataProcessor implementations
    provides com.example.jpms.core.spi.DataProcessor 
        with com.example.jpms.userservice.internal.JsonDataProcessor,
             com.example.jpms.userservice.internal.XmlDataProcessor;
}
