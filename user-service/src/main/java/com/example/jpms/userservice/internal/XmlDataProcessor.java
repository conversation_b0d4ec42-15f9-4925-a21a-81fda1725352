package com.example.jpms.userservice.internal;

import com.example.jpms.core.spi.DataProcessor;
import com.example.jpms.core.spi.ProcessingException;
import com.example.jpms.core.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * XML-specific data processor implementation.
 * 
 * This class demonstrates:
 * - Another service provider implementation
 * - Medium priority processor
 * - XML format handling
 */
public final class XmlDataProcessor implements DataProcessor {
    private static final Logger logger = LoggerFactory.getLogger(XmlDataProcessor.class);
    
    @Override
    public String getName() {
        return "XML Data Processor";
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
    
    @Override
    public String process(String data) throws ProcessingException {
        logger.info("Processing XML data with XML processor");
        
        if (data == null) {
            throw new ProcessingException("XML data cannot be null");
        }
        
        try {
            String trimmed = data.trim();
            
            if (StringUtils.isEmpty(trimmed)) {
                return "<empty/>";
            }
            
            // If it looks like XML, format it
            if (trimmed.startsWith("<") && trimmed.endsWith(">")) {
                // Simple XML formatting
                String result = "[XML] " + trimmed;
                logger.debug("Processed XML: '{}' -> '{}'", data, result);
                return result;
            }
            
            // If not XML format, convert to XML
            String capitalized = StringUtils.capitalize(trimmed);
            String result = "<value>" + capitalized + "</value>";
            logger.debug("Converted to XML: '{}' -> '{}'", data, result);
            return result;
            
        } catch (Exception e) {
            logger.error("Error processing XML data: {}", data, e);
            throw new ProcessingException("Failed to process XML data", e);
        }
    }
    
    @Override
    public boolean canProcess(String data) {
        if (data == null) {
            return false;
        }
        
        String trimmed = data.trim();
        // Can process XML-like data or any data that should be converted to XML
        boolean canProcess = trimmed.startsWith("<") || trimmed.contains("xml") || 
                           trimmed.toLowerCase().contains("xml");
        
        logger.debug("Can process XML data '{}': {}", data, canProcess);
        return canProcess;
    }
    
    @Override
    public int getPriority() {
        return 5; // Medium priority
    }
}
