package com.example.jpms.userservice.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;
import java.util.Optional;

/**
 * Data Transfer Object for user update requests.
 * Uses Optional to indicate which fields should be updated.
 */
public final class UpdateUserRequest {
    private final Optional<String> name;
    private final Optional<String> email;
    private final Optional<String> phone;
    
    @JsonCreator
    public UpdateUserRequest(
            @JsonProperty("name") String name,
            @JsonProperty("email") String email,
            @JsonProperty("phone") String phone) {
        this.name = Optional.ofNullable(name);
        this.email = Optional.ofNullable(email);
        this.phone = Optional.ofNullable(phone);
    }
    
    // Convenience constructors
    public static UpdateUserRequest withName(String name) {
        return new UpdateUserRequest(name, null, null);
    }
    
    public static UpdateUserRequest withEmail(String email) {
        return new UpdateUserRequest(null, email, null);
    }
    
    public static UpdateUserRequest withPhone(String phone) {
        return new UpdateUserRequest(null, null, phone);
    }
    
    @JsonProperty("name")
    public Optional<String> getName() {
        return name;
    }
    
    @JsonProperty("email")
    public Optional<String> getEmail() {
        return email;
    }
    
    @JsonProperty("phone")
    public Optional<String> getPhone() {
        return phone;
    }
    
    /**
     * Checks if this request has any updates.
     * 
     * @return true if at least one field is present for update
     */
    public boolean hasUpdates() {
        return name.isPresent() || email.isPresent() || phone.isPresent();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        UpdateUserRequest that = (UpdateUserRequest) obj;
        return Objects.equals(name, that.name) &&
               Objects.equals(email, that.email) &&
               Objects.equals(phone, that.phone);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, email, phone);
    }
    
    @Override
    public String toString() {
        return "UpdateUserRequest{" +
               "name=" + name +
               ", email=" + email +
               ", phone=" + phone +
               '}';
    }
}
