package com.example.jpms.userservice.api;

/**
 * Exception thrown by user service operations.
 * This exception is part of the exported API package.
 */
public class UserServiceException extends Exception {
    
    private final ErrorCode errorCode;
    
    public UserServiceException(String message) {
        super(message);
        this.errorCode = ErrorCode.GENERAL_ERROR;
    }
    
    public UserServiceException(String message, ErrorCode errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public UserServiceException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = ErrorCode.GENERAL_ERROR;
    }
    
    public UserServiceException(String message, Throwable cause, ErrorCode errorCode) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public ErrorCode getErrorCode() {
        return errorCode;
    }
    
    /**
     * Error codes for user service exceptions.
     */
    public enum ErrorCode {
        GENERAL_ERROR,
        VALIDATION_ERROR,
        USER_NOT_FOUND,
        USER_ALREADY_EXISTS,
        INVALID_EMAIL,
        INVALID_PHONE,
        DATA_PROCESSING_ERROR
    }
}
