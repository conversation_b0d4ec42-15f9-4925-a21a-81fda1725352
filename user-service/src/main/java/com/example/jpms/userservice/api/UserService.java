package com.example.jpms.userservice.api;

import com.example.jpms.core.model.User;
import com.example.jpms.userservice.dto.CreateUserRequest;
import com.example.jpms.userservice.dto.UpdateUserRequest;
import com.example.jpms.userservice.dto.UserResponse;

import java.util.List;
import java.util.Optional;

/**
 * User service interface defining the public API.
 * This interface is exported and can be used by other modules.
 */
public interface UserService {
    
    /**
     * Creates a new user.
     * 
     * @param request the user creation request
     * @return the created user response
     * @throws UserServiceException if user creation fails
     */
    UserResponse createUser(CreateUserRequest request) throws UserServiceException;
    
    /**
     * Retrieves a user by ID.
     * 
     * @param userId the user ID
     * @return the user if found, empty otherwise
     */
    Optional<UserResponse> getUserById(String userId);
    
    /**
     * Retrieves a user by email.
     * 
     * @param email the user email
     * @return the user if found, empty otherwise
     */
    Optional<UserResponse> getUserByEmail(String email);
    
    /**
     * Updates an existing user.
     * 
     * @param userId the user ID
     * @param request the update request
     * @return the updated user response
     * @throws UserServiceException if user update fails
     */
    UserResponse updateUser(String userId, UpdateUserRequest request) throws UserServiceException;
    
    /**
     * Deactivates a user.
     * 
     * @param userId the user ID
     * @return true if user was deactivated, false if user not found
     */
    boolean deactivateUser(String userId);
    
    /**
     * Retrieves all active users.
     * 
     * @return list of active users
     */
    List<UserResponse> getAllActiveUsers();
    
    /**
     * Searches users by name pattern.
     * 
     * @param namePattern the name pattern to search for
     * @return list of matching users
     */
    List<UserResponse> searchUsersByName(String namePattern);
    
    /**
     * Validates user data.
     * 
     * @param user the user to validate
     * @return validation result
     */
    ValidationResult validateUser(User user);
    
    /**
     * Exports user data as JSON.
     * 
     * @param userId the user ID
     * @return JSON representation of the user
     * @throws UserServiceException if export fails
     */
    String exportUserAsJson(String userId) throws UserServiceException;
}
