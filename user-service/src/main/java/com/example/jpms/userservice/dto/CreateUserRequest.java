package com.example.jpms.userservice.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

/**
 * Data Transfer Object for user creation requests.
 * This class is opened to Jackson for reflection-based serialization.
 */
public final class CreateUserRequest {
    private final String name;
    private final String email;
    private final String phone;
    
    @JsonCreator
    public CreateUserRequest(
            @JsonProperty("name") String name,
            @JsonProperty("email") String email,
            @JsonProperty("phone") String phone) {
        this.name = name;
        this.email = email;
        this.phone = phone;
    }
    
    @JsonProperty("name")
    public String getName() {
        return name;
    }
    
    @JsonProperty("email")
    public String getEmail() {
        return email;
    }
    
    @JsonProperty("phone")
    public String getPhone() {
        return phone;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        CreateUserRequest that = (CreateUserRequest) obj;
        return Objects.equals(name, that.name) &&
               Objects.equals(email, that.email) &&
               Objects.equals(phone, that.phone);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, email, phone);
    }
    
    @Override
    public String toString() {
        return "CreateUserRequest{" +
               "name='" + name + '\'' +
               ", email='" + email + '\'' +
               ", phone='" + phone + '\'' +
               '}';
    }
}
