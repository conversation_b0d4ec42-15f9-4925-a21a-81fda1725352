package com.example.jpms.userservice.internal;

import com.example.jpms.core.model.User;
import com.example.jpms.core.spi.DataProcessor;
import com.example.jpms.core.spi.ProcessingException;
import com.example.jpms.core.util.StringUtils;
import com.example.jpms.core.util.ValidationUtils;
import com.example.jpms.userservice.api.UserService;
import com.example.jpms.userservice.api.UserServiceException;
import com.example.jpms.userservice.api.ValidationResult;
import com.example.jpms.userservice.dto.CreateUserRequest;
import com.example.jpms.userservice.dto.UpdateUserRequest;
import com.example.jpms.userservice.dto.UserResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * Implementation of UserService.
 * 
 * This class demonstrates:
 * - Internal implementation (not exported)
 * - Use of utilities from core module
 * - Service loading and usage
 * - Integration with external libraries
 */
public final class UserServiceImpl implements UserService {
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    
    private final Map<String, User> users = new ConcurrentHashMap<>();
    private final Map<String, User> usersByEmail = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    private final ObjectMapper objectMapper;
    private final List<DataProcessor> dataProcessors;
    
    public UserServiceImpl() {
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        
        // Load data processors using ServiceLoader
        this.dataProcessors = ServiceLoader.load(DataProcessor.class)
                .stream()
                .map(ServiceLoader.Provider::get)
                .sorted((p1, p2) -> Integer.compare(p2.getPriority(), p1.getPriority()))
                .collect(Collectors.toList());
        
        logger.info("Loaded {} data processors", dataProcessors.size());
        dataProcessors.forEach(processor -> 
            logger.info("  - {} v{} (priority: {})", 
                processor.getName(), processor.getVersion(), processor.getPriority()));
    }
    
    @Override
    public UserResponse createUser(CreateUserRequest request) throws UserServiceException {
        logger.info("Creating user with email: {}", request.getEmail());
        
        // Validate request
        ValidationResult validation = validateCreateRequest(request);
        if (!validation.isValid()) {
            throw new UserServiceException(
                "Validation failed: " + String.join(", ", validation.getErrors()),
                UserServiceException.ErrorCode.VALIDATION_ERROR
            );
        }
        
        // Check if user already exists
        if (usersByEmail.containsKey(request.getEmail())) {
            throw new UserServiceException(
                "User with email " + request.getEmail() + " already exists",
                UserServiceException.ErrorCode.USER_ALREADY_EXISTS
            );
        }
        
        // Process user name using data processors
        String processedName = processData(request.getName());
        
        // Create user
        String userId = String.valueOf(idGenerator.getAndIncrement());
        User user = new User(userId, processedName, request.getEmail(), request.getPhone());
        
        // Store user
        users.put(userId, user);
        usersByEmail.put(request.getEmail(), user);
        
        logger.info("Created user with ID: {}", userId);
        return UserResponse.fromUser(user);
    }
    
    @Override
    public Optional<UserResponse> getUserById(String userId) {
        logger.debug("Getting user by ID: {}", userId);
        return Optional.ofNullable(users.get(userId))
                .map(UserResponse::fromUser);
    }
    
    @Override
    public Optional<UserResponse> getUserByEmail(String email) {
        logger.debug("Getting user by email: {}", StringUtils.maskSensitive(email));
        return Optional.ofNullable(usersByEmail.get(email))
                .map(UserResponse::fromUser);
    }
    
    @Override
    public UserResponse updateUser(String userId, UpdateUserRequest request) throws UserServiceException {
        logger.info("Updating user: {}", userId);
        
        User existingUser = users.get(userId);
        if (existingUser == null) {
            throw new UserServiceException(
                "User not found: " + userId,
                UserServiceException.ErrorCode.USER_NOT_FOUND
            );
        }
        
        if (!request.hasUpdates()) {
            return UserResponse.fromUser(existingUser);
        }
        
        // Validate update request
        ValidationResult validation = validateUpdateRequest(request, existingUser);
        if (!validation.isValid()) {
            throw new UserServiceException(
                "Validation failed: " + String.join(", ", validation.getErrors()),
                UserServiceException.ErrorCode.VALIDATION_ERROR
            );
        }
        
        // Apply updates
        User updatedUser = existingUser;
        
        if (request.getName().isPresent()) {
            String processedName = processData(request.getName().get());
            updatedUser = updatedUser.withName(processedName);
        }
        
        if (request.getEmail().isPresent()) {
            // Remove old email mapping
            usersByEmail.remove(existingUser.getEmail());
            // Add new email mapping will be done after user is updated
        }
        
        // Update user in storage
        users.put(userId, updatedUser);
        
        if (request.getEmail().isPresent()) {
            usersByEmail.put(updatedUser.getEmail(), updatedUser);
        }
        
        logger.info("Updated user: {}", userId);
        return UserResponse.fromUser(updatedUser);
    }
    
    @Override
    public boolean deactivateUser(String userId) {
        logger.info("Deactivating user: {}", userId);

        User existingUser = users.get(userId);
        if (existingUser == null) {
            logger.warn("User not found for deactivation: {}", userId);
            return false;
        }

        User deactivatedUser = existingUser.withActive(false);
        users.put(userId, deactivatedUser);
        usersByEmail.put(existingUser.getEmail(), deactivatedUser);

        logger.info("Deactivated user: {}", userId);
        return true;
    }

    @Override
    public List<UserResponse> getAllActiveUsers() {
        logger.debug("Getting all active users");
        return users.values().stream()
                .filter(User::isActive)
                .map(UserResponse::fromUser)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserResponse> searchUsersByName(String namePattern) {
        logger.debug("Searching users by name pattern: {}", namePattern);

        if (StringUtils.isEmpty(namePattern)) {
            return Collections.emptyList();
        }

        String lowerPattern = namePattern.toLowerCase();
        return users.values().stream()
                .filter(User::isActive)
                .filter(user -> user.getName().toLowerCase().contains(lowerPattern))
                .map(UserResponse::fromUser)
                .collect(Collectors.toList());
    }

    @Override
    public ValidationResult validateUser(User user) {
        List<String> errors = new ArrayList<>();

        if (user == null) {
            return ValidationResult.failure("User cannot be null");
        }

        if (StringUtils.isEmpty(user.getName())) {
            errors.add("Name is required");
        } else if (!ValidationUtils.isValidLength(user.getName(), 2, 100)) {
            errors.add("Name must be between 2 and 100 characters");
        }

        if (StringUtils.isEmpty(user.getEmail())) {
            errors.add("Email is required");
        } else if (!ValidationUtils.isValidEmail(user.getEmail())) {
            errors.add("Invalid email format");
        }

        if (!StringUtils.isEmpty(user.getPhone()) && !ValidationUtils.isValidPhone(user.getPhone())) {
            errors.add("Invalid phone format");
        }

        return errors.isEmpty() ? ValidationResult.success() : ValidationResult.failure(errors);
    }

    @Override
    public String exportUserAsJson(String userId) throws UserServiceException {
        logger.debug("Exporting user as JSON: {}", userId);

        User user = users.get(userId);
        if (user == null) {
            throw new UserServiceException(
                "User not found: " + userId,
                UserServiceException.ErrorCode.USER_NOT_FOUND
            );
        }

        try {
            return objectMapper.writeValueAsString(UserResponse.fromUser(user));
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize user to JSON: {}", userId, e);
            throw new UserServiceException(
                "Failed to export user as JSON",
                e,
                UserServiceException.ErrorCode.DATA_PROCESSING_ERROR
            );
        }
    }

    // Private helper methods

    private ValidationResult validateCreateRequest(CreateUserRequest request) {
        List<String> errors = new ArrayList<>();

        if (StringUtils.isEmpty(request.getName())) {
            errors.add("Name is required");
        } else if (!ValidationUtils.isValidLength(request.getName(), 2, 100)) {
            errors.add("Name must be between 2 and 100 characters");
        }

        if (StringUtils.isEmpty(request.getEmail())) {
            errors.add("Email is required");
        } else if (!ValidationUtils.isValidEmail(request.getEmail())) {
            errors.add("Invalid email format");
        }

        if (!StringUtils.isEmpty(request.getPhone()) && !ValidationUtils.isValidPhone(request.getPhone())) {
            errors.add("Invalid phone format");
        }

        return errors.isEmpty() ? ValidationResult.success() : ValidationResult.failure(errors);
    }

    private ValidationResult validateUpdateRequest(UpdateUserRequest request, User existingUser) {
        List<String> errors = new ArrayList<>();

        if (request.getName().isPresent()) {
            String name = request.getName().get();
            if (StringUtils.isEmpty(name)) {
                errors.add("Name cannot be empty");
            } else if (!ValidationUtils.isValidLength(name, 2, 100)) {
                errors.add("Name must be between 2 and 100 characters");
            }
        }

        if (request.getEmail().isPresent()) {
            String email = request.getEmail().get();
            if (StringUtils.isEmpty(email)) {
                errors.add("Email cannot be empty");
            } else if (!ValidationUtils.isValidEmail(email)) {
                errors.add("Invalid email format");
            } else if (!email.equals(existingUser.getEmail()) && usersByEmail.containsKey(email)) {
                errors.add("Email already exists");
            }
        }

        if (request.getPhone().isPresent()) {
            String phone = request.getPhone().get();
            if (!StringUtils.isEmpty(phone) && !ValidationUtils.isValidPhone(phone)) {
                errors.add("Invalid phone format");
            }
        }

        return errors.isEmpty() ? ValidationResult.success() : ValidationResult.failure(errors);
    }

    private String processData(String data) {
        for (DataProcessor processor : dataProcessors) {
            if (processor.canProcess(data)) {
                try {
                    String processed = processor.process(data);
                    logger.debug("Processed data with {}: '{}' -> '{}'",
                        processor.getName(), data, processed);
                    return processed;
                } catch (ProcessingException e) {
                    logger.warn("Failed to process data with {}: {}",
                        processor.getName(), e.getMessage());
                    // Continue to next processor
                }
            }
        }

        // If no processor could handle the data, return as-is
        logger.debug("No processor could handle data, returning as-is: {}", data);
        return data;
    }
}
