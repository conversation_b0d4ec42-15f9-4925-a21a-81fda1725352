package com.example.jpms.userservice.internal;

import com.example.jpms.core.spi.DataProcessor;
import com.example.jpms.core.spi.ProcessingException;
import com.example.jpms.core.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * JSON-specific data processor implementation.
 * 
 * This class demonstrates:
 * - Additional service provider implementation
 * - Higher priority processor
 * - Specific data format handling
 */
public final class JsonDataProcessor implements DataProcessor {
    private static final Logger logger = LoggerFactory.getLogger(JsonDataProcessor.class);
    
    @Override
    public String getName() {
        return "JSON Data Processor";
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
    
    @Override
    public String process(String data) throws ProcessingException {
        logger.info("Processing JSON data with JSON processor");
        
        if (data == null) {
            throw new ProcessingException("JSON data cannot be null");
        }
        
        try {
            // Simple JSON-like processing: ensure proper formatting
            String trimmed = data.trim();
            
            if (StringUtils.isEmpty(trimmed)) {
                return "{}";
            }
            
            // If it looks like JSON, format it nicely
            if (trimmed.startsWith("{") && trimmed.endsWith("}")) {
                // Simple JSON formatting (in real implementation, use proper JSON parser)
                String formatted = trimmed.replaceAll("\\s*,\\s*", ", ")
                                         .replaceAll("\\s*:\\s*", ": ");
                String result = "[JSON] " + formatted;
                logger.debug("Processed JSON: '{}' -> '{}'", data, result);
                return result;
            }
            
            // If not JSON format, convert to JSON-like string
            String capitalized = StringUtils.capitalize(trimmed);
            String result = "{\"value\": \"" + capitalized + "\"}";
            logger.debug("Converted to JSON: '{}' -> '{}'", data, result);
            return result;
            
        } catch (Exception e) {
            logger.error("Error processing JSON data: {}", data, e);
            throw new ProcessingException("Failed to process JSON data", e);
        }
    }
    
    @Override
    public boolean canProcess(String data) {
        if (data == null) {
            return false;
        }
        
        String trimmed = data.trim();
        // Can process JSON-like data or any data that should be converted to JSON
        boolean canProcess = trimmed.startsWith("{") || trimmed.contains("json") || 
                           trimmed.toLowerCase().contains("json");
        
        logger.debug("Can process JSON data '{}': {}", data, canProcess);
        return canProcess;
    }
    
    @Override
    public int getPriority() {
        return 10; // Higher priority than default processor
    }
}
