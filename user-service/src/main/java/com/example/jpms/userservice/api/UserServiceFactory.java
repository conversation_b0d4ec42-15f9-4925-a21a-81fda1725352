package com.example.jpms.userservice.api;

import com.example.jpms.userservice.internal.UserServiceImpl;

/**
 * Factory class for creating UserService instances.
 * This provides a clean way to obtain service instances without
 * exposing the implementation classes.
 */
public final class UserServiceFactory {
    
    private static volatile UserService instance;
    
    // Private constructor to prevent instantiation
    private UserServiceFactory() {
        throw new UnsupportedOperationException("Factory class cannot be instantiated");
    }
    
    /**
     * Gets a singleton instance of UserService.
     * 
     * @return the UserService instance
     */
    public static UserService getInstance() {
        if (instance == null) {
            synchronized (UserServiceFactory.class) {
                if (instance == null) {
                    instance = new UserServiceImpl();
                }
            }
        }
        return instance;
    }
    
    /**
     * Creates a new instance of UserService.
     * 
     * @return a new UserService instance
     */
    public static UserService createInstance() {
        return new UserServiceImpl();
    }
}
