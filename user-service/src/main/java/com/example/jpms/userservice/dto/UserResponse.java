package com.example.jpms.userservice.dto;

import com.example.jpms.core.model.User;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Data Transfer Object for user responses.
 * This class converts internal User model to external representation.
 */
public final class UserResponse {
    private final String id;
    private final String name;
    private final String email;
    private final String phone;
    private final LocalDateTime createdAt;
    private final boolean active;
    
    @JsonCreator
    public UserResponse(
            @JsonProperty("id") String id,
            @JsonProperty("name") String name,
            @JsonProperty("email") String email,
            @JsonProperty("phone") String phone,
            @JsonProperty("createdAt") LocalDateTime createdAt,
            @JsonProperty("active") boolean active) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.phone = phone;
        this.createdAt = createdAt;
        this.active = active;
    }
    
    /**
     * Creates a UserResponse from a User model.
     * 
     * @param user the user model
     * @return the user response
     */
    public static UserResponse fromUser(User user) {
        return new UserResponse(
            user.getId(),
            user.getName(),
            user.getEmail(),
            user.getPhone(),
            user.getCreatedAt(),
            user.isActive()
        );
    }
    
    /**
     * Converts this response back to a User model.
     * 
     * @return the user model
     */
    public User toUser() {
        return new User(id, name, email, phone, createdAt, active);
    }
    
    @JsonProperty("id")
    public String getId() {
        return id;
    }
    
    @JsonProperty("name")
    public String getName() {
        return name;
    }
    
    @JsonProperty("email")
    public String getEmail() {
        return email;
    }
    
    @JsonProperty("phone")
    public String getPhone() {
        return phone;
    }
    
    @JsonProperty("createdAt")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    @JsonProperty("active")
    public boolean isActive() {
        return active;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        UserResponse that = (UserResponse) obj;
        return active == that.active &&
               Objects.equals(id, that.id) &&
               Objects.equals(name, that.name) &&
               Objects.equals(email, that.email) &&
               Objects.equals(phone, that.phone) &&
               Objects.equals(createdAt, that.createdAt);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, name, email, phone, createdAt, active);
    }
    
    @Override
    public String toString() {
        return "UserResponse{" +
               "id='" + id + '\'' +
               ", name='" + name + '\'' +
               ", email='" + email + '\'' +
               ", phone='" + phone + '\'' +
               ", createdAt=" + createdAt +
               ", active=" + active +
               '}';
    }
}
