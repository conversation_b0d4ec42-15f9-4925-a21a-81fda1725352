package com.example.jpms.userservice.api;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Validation result containing validation status and error messages.
 * This class is part of the exported API package.
 */
public final class ValidationResult {
    private final boolean valid;
    private final List<String> errors;
    
    private ValidationResult(boolean valid, List<String> errors) {
        this.valid = valid;
        this.errors = Collections.unmodifiableList(new ArrayList<>(errors));
    }
    
    /**
     * Creates a successful validation result.
     * 
     * @return a valid validation result
     */
    public static ValidationResult success() {
        return new ValidationResult(true, Collections.emptyList());
    }
    
    /**
     * Creates a failed validation result with errors.
     * 
     * @param errors the validation errors
     * @return an invalid validation result
     */
    public static ValidationResult failure(List<String> errors) {
        return new ValidationResult(false, errors);
    }
    
    /**
     * Creates a failed validation result with a single error.
     * 
     * @param error the validation error
     * @return an invalid validation result
     */
    public static ValidationResult failure(String error) {
        return new ValidationResult(false, List.of(error));
    }
    
    /**
     * Checks if the validation was successful.
     * 
     * @return true if valid, false otherwise
     */
    public boolean isValid() {
        return valid;
    }
    
    /**
     * Gets the validation errors.
     * 
     * @return list of validation errors (empty if valid)
     */
    public List<String> getErrors() {
        return errors;
    }
    
    /**
     * Gets the first validation error.
     * 
     * @return the first error message, or null if valid
     */
    public String getFirstError() {
        return errors.isEmpty() ? null : errors.get(0);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ValidationResult that = (ValidationResult) obj;
        return valid == that.valid && Objects.equals(errors, that.errors);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(valid, errors);
    }
    
    @Override
    public String toString() {
        return "ValidationResult{" +
               "valid=" + valid +
               ", errors=" + errors +
               '}';
    }
}
