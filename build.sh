#!/bin/bash

# 创建输出目录
mkdir -p out/service-api out/service-impl out/app

# 编译服务API模块
javac -d out/service-api \
    service-api/src/main/java/module-info.java \
    service-api/src/main/java/com/demo/service/*.java

# 编译服务实现模块
javac -d out/service-impl \
    --module-path out \
    service-impl/src/main/java/module-info.java \
    service-impl/src/main/java/com/demo/service/impl/*.java

# 编译应用模块
javac -d out/app \
    --module-path out \
    app/src/main/java/module-info.java \
    app/src/main/java/com/demo/app/*.java

echo "编译完成!"