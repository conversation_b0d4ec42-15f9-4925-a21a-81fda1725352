package com.example.jpms.core.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for StringUtils class.
 * Demonstrates testing within a module.
 */
@DisplayName("StringUtils Tests")
class StringUtilsTest {
    
    @Test
    @DisplayName("isEmpty should return true for null and empty strings")
    void testIsEmpty() {
        assertTrue(StringUtils.isEmpty(null));
        assertTrue(StringUtils.isEmpty(""));
        assertTrue(StringUtils.isEmpty("   "));
        assertFalse(StringUtils.isEmpty("hello"));
        assertFalse(StringUtils.isEmpty(" hello "));
    }
    
    @Test
    @DisplayName("capitalize should properly capitalize words")
    void testCapitalize() {
        assertEquals("Hello World", StringUtils.capitalize("hello world"));
        assertEquals("Hello World", StringUtils.capitalize("HELLO WORLD"));
        assertEquals("Hello World", StringUtils.capitalize("hELLO wORLD"));
        assertEquals("", StringUtils.capitalize(""));
        assertNull(StringUtils.capitalize(null));
        assertEquals("A", StringUtils.capitalize("a"));
        assertEquals("Hello", StringUtils.capitalize("hello"));
    }
    
    @Test
    @DisplayName("reverse should properly reverse strings")
    void testReverse() {
        assertEquals("olleh", StringUtils.reverse("hello"));
        assertEquals("dlrow olleh", StringUtils.reverse("hello world"));
        assertEquals("", StringUtils.reverse(""));
        assertNull(StringUtils.reverse(null));
        assertEquals("a", StringUtils.reverse("a"));
    }
    
    @Test
    @DisplayName("maskSensitive should properly mask sensitive information")
    void testMaskSensitive() {
        assertEquals("he***lo", StringUtils.maskSensitive("hello"));
        assertEquals("he*****ld", StringUtils.maskSensitive("helloworld"));
        assertEquals("****", StringUtils.maskSensitive("test"));
        assertEquals("***", StringUtils.maskSensitive("abc"));
        assertEquals("**", StringUtils.maskSensitive("ab"));
        assertEquals("*", StringUtils.maskSensitive("a"));
        assertEquals("", StringUtils.maskSensitive(""));
        assertNull(StringUtils.maskSensitive(null));
    }
}
