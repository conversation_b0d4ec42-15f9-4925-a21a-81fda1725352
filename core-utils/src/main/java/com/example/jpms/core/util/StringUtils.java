package com.example.jpms.core.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * String utility class demonstrating module encapsulation.
 * This class is exported and can be used by other modules.
 */
public final class StringUtils {
    private static final Logger logger = LoggerFactory.getLogger(StringUtils.class);
    
    // Private constructor to prevent instantiation
    private StringUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    /**
     * Checks if a string is null or empty.
     * 
     * @param str the string to check
     * @return true if the string is null or empty, false otherwise
     */
    public static boolean isEmpty(String str) {
        logger.debug("Checking if string is empty: {}", str);
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * Capitalizes the first letter of each word in a string.
     * 
     * @param str the string to capitalize
     * @return the capitalized string
     */
    public static String capitalize(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        logger.debug("Capitalizing string: {}", str);
        
        String[] words = str.split("\\s+");
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < words.length; i++) {
            if (i > 0) {
                result.append(" ");
            }
            
            String word = words[i];
            if (!word.isEmpty()) {
                result.append(Character.toUpperCase(word.charAt(0)));
                if (word.length() > 1) {
                    result.append(word.substring(1).toLowerCase());
                }
            }
        }
        
        return result.toString();
    }
    
    /**
     * Reverses a string.
     * 
     * @param str the string to reverse
     * @return the reversed string
     */
    public static String reverse(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        logger.debug("Reversing string: {}", str);
        return new StringBuilder(str).reverse().toString();
    }
    
    /**
     * Masks sensitive information in a string.
     * This method demonstrates internal utility that might be used
     * across the module but not exposed to other modules.
     * 
     * @param sensitive the sensitive string to mask
     * @return the masked string
     */
    public static String maskSensitive(String sensitive) {
        if (isEmpty(sensitive)) {
            return sensitive;
        }
        
        if (sensitive.length() <= 4) {
            return "*".repeat(sensitive.length());
        }
        
        return sensitive.substring(0, 2) + "*".repeat(sensitive.length() - 4) + 
               sensitive.substring(sensitive.length() - 2);
    }
}
