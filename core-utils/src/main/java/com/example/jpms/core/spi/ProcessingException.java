package com.example.jpms.core.spi;

/**
 * Exception thrown when data processing fails.
 * This exception is part of the exported SPI package.
 */
public class ProcessingException extends Exception {
    
    public ProcessingException(String message) {
        super(message);
    }
    
    public ProcessingException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public ProcessingException(Throwable cause) {
        super(cause);
    }
}
