package com.example.jpms.core.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

/**
 * Validation utility class providing common validation methods.
 * This class demonstrates exported utility functionality.
 */
public final class ValidationUtils {
    private static final Logger logger = LoggerFactory.getLogger(ValidationUtils.class);
    
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$"
    );
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^\\+?[1-9]\\d{1,14}$"
    );
    
    // Private constructor to prevent instantiation
    private ValidationUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    /**
     * Validates an email address.
     * 
     * @param email the email to validate
     * @return true if the email is valid, false otherwise
     */
    public static boolean isValidEmail(String email) {
        if (StringUtils.isEmpty(email)) {
            logger.debug("Email validation failed: empty email");
            return false;
        }
        
        boolean isValid = EMAIL_PATTERN.matcher(email).matches();
        logger.debug("Email validation for '{}': {}", 
                    StringUtils.maskSensitive(email), isValid);
        return isValid;
    }
    
    /**
     * Validates a phone number.
     * 
     * @param phone the phone number to validate
     * @return true if the phone number is valid, false otherwise
     */
    public static boolean isValidPhone(String phone) {
        if (StringUtils.isEmpty(phone)) {
            logger.debug("Phone validation failed: empty phone");
            return false;
        }
        
        boolean isValid = PHONE_PATTERN.matcher(phone).matches();
        logger.debug("Phone validation for '{}': {}", 
                    StringUtils.maskSensitive(phone), isValid);
        return isValid;
    }
    
    /**
     * Validates that a string is within specified length bounds.
     * 
     * @param str the string to validate
     * @param minLength minimum length (inclusive)
     * @param maxLength maximum length (inclusive)
     * @return true if the string length is within bounds, false otherwise
     */
    public static boolean isValidLength(String str, int minLength, int maxLength) {
        if (str == null) {
            logger.debug("Length validation failed: null string");
            return false;
        }
        
        int length = str.length();
        boolean isValid = length >= minLength && length <= maxLength;
        logger.debug("Length validation for string of length {}: {} (min: {}, max: {})", 
                    length, isValid, minLength, maxLength);
        return isValid;
    }
    
    /**
     * Validates that a number is within specified bounds.
     * 
     * @param value the value to validate
     * @param min minimum value (inclusive)
     * @param max maximum value (inclusive)
     * @return true if the value is within bounds, false otherwise
     */
    public static boolean isWithinRange(int value, int min, int max) {
        boolean isValid = value >= min && value <= max;
        logger.debug("Range validation for value {}: {} (min: {}, max: {})", 
                    value, isValid, min, max);
        return isValid;
    }
}
