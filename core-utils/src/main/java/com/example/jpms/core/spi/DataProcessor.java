package com.example.jpms.core.spi;

/**
 * Service Provider Interface (SPI) for data processing.
 * 
 * This interface demonstrates the SPI pattern in JPMS:
 * - The interface is exported for other modules to use
 * - Implementations can be provided by this module or other modules
 * - Service loading is handled through the module system
 */
public interface DataProcessor {
    
    /**
     * Gets the name of this data processor.
     * 
     * @return the processor name
     */
    String getName();
    
    /**
     * Gets the version of this data processor.
     * 
     * @return the processor version
     */
    String getVersion();
    
    /**
     * Processes the given data and returns the result.
     * 
     * @param data the data to process
     * @return the processed data
     * @throws ProcessingException if processing fails
     */
    String process(String data) throws ProcessingException;
    
    /**
     * Validates whether this processor can handle the given data.
     * 
     * @param data the data to validate
     * @return true if this processor can handle the data, false otherwise
     */
    boolean canProcess(String data);
    
    /**
     * Gets the priority of this processor.
     * Higher values indicate higher priority.
     * 
     * @return the processor priority
     */
    default int getPriority() {
        return 0;
    }
}
