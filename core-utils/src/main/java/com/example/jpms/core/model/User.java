package com.example.jpms.core.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * User model class demonstrating:
 * - Module exports for model classes
 * - Jackson integration with modules
 * - Proper encapsulation
 * 
 * This class is opened to Jackson for reflection-based serialization.
 */
public final class User {
    private final String id;
    private final String name;
    private final String email;
    private final String phone;
    private final LocalDateTime createdAt;
    private final boolean active;
    
    @JsonCreator
    public User(
            @JsonProperty("id") String id,
            @JsonProperty("name") String name,
            @JsonProperty("email") String email,
            @JsonProperty("phone") String phone,
            @JsonProperty("createdAt") LocalDateTime createdAt,
            @JsonProperty("active") boolean active) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.phone = phone;
        this.createdAt = createdAt != null ? createdAt : LocalDateTime.now();
        this.active = active;
    }
    
    // Convenience constructor
    public User(String id, String name, String email, String phone) {
        this(id, name, email, phone, LocalDateTime.now(), true);
    }
    
    @JsonProperty("id")
    public String getId() {
        return id;
    }
    
    @JsonProperty("name")
    public String getName() {
        return name;
    }
    
    @JsonProperty("email")
    public String getEmail() {
        return email;
    }
    
    @JsonProperty("phone")
    public String getPhone() {
        return phone;
    }
    
    @JsonProperty("createdAt")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    @JsonProperty("active")
    public boolean isActive() {
        return active;
    }
    
    /**
     * Creates a new User with updated active status.
     * 
     * @param active the new active status
     * @return a new User instance with updated status
     */
    public User withActive(boolean active) {
        return new User(this.id, this.name, this.email, this.phone, this.createdAt, active);
    }
    
    /**
     * Creates a new User with updated name.
     * 
     * @param name the new name
     * @return a new User instance with updated name
     */
    public User withName(String name) {
        return new User(this.id, name, this.email, this.phone, this.createdAt, this.active);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        User user = (User) obj;
        return active == user.active &&
               Objects.equals(id, user.id) &&
               Objects.equals(name, user.name) &&
               Objects.equals(email, user.email) &&
               Objects.equals(phone, user.phone) &&
               Objects.equals(createdAt, user.createdAt);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, name, email, phone, createdAt, active);
    }
    
    @Override
    public String toString() {
        return "User{" +
               "id='" + id + '\'' +
               ", name='" + name + '\'' +
               ", email='" + email + '\'' +
               ", phone='" + phone + '\'' +
               ", createdAt=" + createdAt +
               ", active=" + active +
               '}';
    }
}
