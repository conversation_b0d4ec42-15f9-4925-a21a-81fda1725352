package com.example.jpms.core.internal;

import com.example.jpms.core.spi.DataProcessor;
import com.example.jpms.core.spi.ProcessingException;
import com.example.jpms.core.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Default implementation of DataProcessor.
 * 
 * This class demonstrates:
 * - Internal implementation (not exported)
 * - Service provider implementation
 * - Use of other utilities within the same module
 * 
 * Note: This package is NOT exported in module-info.java,
 * so this class is not accessible to other modules directly.
 * It's only accessible through the SPI mechanism.
 */
public final class DefaultDataProcessor implements DataProcessor {
    private static final Logger logger = LoggerFactory.getLogger(DefaultDataProcessor.class);
    
    @Override
    public String getName() {
        return "Default Data Processor";
    }
    
    @Override
    public String getVersion() {
        return "1.0.0";
    }
    
    @Override
    public String process(String data) throws ProcessingException {
        logger.info("Processing data with default processor");
        
        if (data == null) {
            throw new ProcessingException("Data cannot be null");
        }
        
        try {
            // Simple processing: trim, capitalize, and add prefix
            String trimmed = data.trim();
            if (StringUtils.isEmpty(trimmed)) {
                return "[EMPTY]";
            }
            
            String capitalized = StringUtils.capitalize(trimmed);
            String result = "[PROCESSED] " + capitalized;
            
            logger.debug("Processed '{}' -> '{}'", data, result);
            return result;
            
        } catch (Exception e) {
            logger.error("Error processing data: {}", data, e);
            throw new ProcessingException("Failed to process data", e);
        }
    }
    
    @Override
    public boolean canProcess(String data) {
        // Default processor can handle any non-null data
        boolean canProcess = data != null;
        logger.debug("Can process data '{}': {}", data, canProcess);
        return canProcess;
    }
    
    @Override
    public int getPriority() {
        return 1; // Low priority as this is the default implementation
    }
}
