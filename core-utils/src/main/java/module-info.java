/**
 * Core utilities module providing common functionality for the JPMS demo application.
 * 
 * This module demonstrates:
 * - Basic module exports and encapsulation
 * - Transitive dependencies
 * - Service provider interface (SPI) pattern
 * - Integration with external libraries (Jackson, SLF4J)
 */
module com.example.jpms.core {
    // External dependencies
    requires com.fasterxml.jackson.core;
    requires com.fasterxml.jackson.databind;

    // Export public API packages
    exports com.example.jpms.core.util;
    exports com.example.jpms.core.model;
    exports com.example.jpms.core.spi;

    // Export transitive dependencies to modules that depend on this module
    requires transitive org.slf4j;
    
    // Open packages for reflection (needed for Jackson serialization)
    opens com.example.jpms.core.model to com.fasterxml.jackson.databind;
    
    // Service provider interface declarations
    uses com.example.jpms.core.spi.DataProcessor;
    
    // Provide default implementation of the service
    provides com.example.jpms.core.spi.DataProcessor 
        with com.example.jpms.core.internal.DefaultDataProcessor;
}
