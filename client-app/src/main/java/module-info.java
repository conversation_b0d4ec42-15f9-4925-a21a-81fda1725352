/**
 * Client application module demonstrating complete module dependency chain.
 * 
 * This module demonstrates:
 * - Module dependency chain (client -> user-service -> core-utils)
 * - Transitive dependencies
 * - Service consumption
 * - Logging integration
 * - Main application entry point
 */
module com.example.jpms.client {
    // Dependency on user service module (which transitively includes core-utils)
    requires com.example.jpms.userservice;
    
    // Logging dependencies
    requires ch.qos.logback.classic;
    requires ch.qos.logback.core;
    
    // Note: We don't need to explicitly require core-utils or slf4j
    // because they are transitively available through user-service
    
    // We don't export any packages since this is an application module
    // (end consumer, not a library)
}
