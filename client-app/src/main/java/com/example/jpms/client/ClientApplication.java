package com.example.jpms.client;

import com.example.jpms.userservice.api.UserService;
import com.example.jpms.userservice.api.UserServiceException;
import com.example.jpms.userservice.api.UserServiceFactory;
import com.example.jpms.userservice.dto.CreateUserRequest;
import com.example.jpms.userservice.dto.UpdateUserRequest;
import com.example.jpms.userservice.dto.UserResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;
import java.util.Scanner;

/**
 * Main client application demonstrating the use of modular services.
 * 
 * This class demonstrates:
 * - Using services from other modules
 * - Complete module dependency chain
 * - Error handling across modules
 * - Interactive console application
 */
public final class ClientApplication {
    private static final Logger logger = LoggerFactory.getLogger(ClientApplication.class);
    
    private final UserService userService;
    private final Scanner scanner;
    
    public ClientApplication() {
        this.userService = UserServiceFactory.getInstance();
        this.scanner = new Scanner(System.in);
    }
    
    public static void main(String[] args) {
        logger.info("Starting Java Modules Demo Client Application");
        
        ClientApplication app = new ClientApplication();
        app.run();
        
        logger.info("Client Application finished");
    }
    
    public void run() {
        System.out.println("=== Java Modules Demo - User Management System ===");
        System.out.println("This application demonstrates JPMS features:");
        System.out.println("- Module dependencies and encapsulation");
        System.out.println("- Service Provider Interface (SPI) pattern");
        System.out.println("- Transitive dependencies");
        System.out.println("- Integration with external libraries");
        System.out.println();
        
        boolean running = true;
        while (running) {
            showMenu();
            String choice = scanner.nextLine().trim();
            
            try {
                switch (choice) {
                    case "1" -> createUser();
                    case "2" -> getUserById();
                    case "3" -> getUserByEmail();
                    case "4" -> updateUser();
                    case "5" -> deactivateUser();
                    case "6" -> listAllUsers();
                    case "7" -> searchUsers();
                    case "8" -> exportUser();
                    case "9" -> demonstrateDataProcessors();
                    case "0" -> {
                        running = false;
                        System.out.println("Goodbye!");
                    }
                    default -> System.out.println("Invalid choice. Please try again.");
                }
            } catch (Exception e) {
                logger.error("Error processing user input", e);
                System.out.println("Error: " + e.getMessage());
            }
            
            if (running) {
                System.out.println("\nPress Enter to continue...");
                scanner.nextLine();
            }
        }
    }
    
    private void showMenu() {
        System.out.println("\n=== User Management Menu ===");
        System.out.println("1. Create User");
        System.out.println("2. Get User by ID");
        System.out.println("3. Get User by Email");
        System.out.println("4. Update User");
        System.out.println("5. Deactivate User");
        System.out.println("6. List All Active Users");
        System.out.println("7. Search Users by Name");
        System.out.println("8. Export User as JSON");
        System.out.println("9. Demonstrate Data Processors");
        System.out.println("0. Exit");
        System.out.print("Choose an option: ");
    }
    
    private void createUser() throws UserServiceException {
        System.out.println("\n--- Create User ---");
        
        System.out.print("Enter name: ");
        String name = scanner.nextLine().trim();
        
        System.out.print("Enter email: ");
        String email = scanner.nextLine().trim();
        
        System.out.print("Enter phone (optional): ");
        String phone = scanner.nextLine().trim();
        if (phone.isEmpty()) {
            phone = null;
        }
        
        CreateUserRequest request = new CreateUserRequest(name, email, phone);
        UserResponse user = userService.createUser(request);
        
        System.out.println("User created successfully:");
        printUser(user);
    }
    
    private void getUserById() {
        System.out.println("\n--- Get User by ID ---");
        
        System.out.print("Enter user ID: ");
        String userId = scanner.nextLine().trim();
        
        Optional<UserResponse> user = userService.getUserById(userId);
        if (user.isPresent()) {
            System.out.println("User found:");
            printUser(user.get());
        } else {
            System.out.println("User not found with ID: " + userId);
        }
    }
    
    private void getUserByEmail() {
        System.out.println("\n--- Get User by Email ---");
        
        System.out.print("Enter email: ");
        String email = scanner.nextLine().trim();
        
        Optional<UserResponse> user = userService.getUserByEmail(email);
        if (user.isPresent()) {
            System.out.println("User found:");
            printUser(user.get());
        } else {
            System.out.println("User not found with email: " + email);
        }
    }
    
    private void updateUser() throws UserServiceException {
        System.out.println("\n--- Update User ---");
        
        System.out.print("Enter user ID: ");
        String userId = scanner.nextLine().trim();
        
        // Check if user exists
        Optional<UserResponse> existingUser = userService.getUserById(userId);
        if (existingUser.isEmpty()) {
            System.out.println("User not found with ID: " + userId);
            return;
        }
        
        System.out.println("Current user details:");
        printUser(existingUser.get());
        
        System.out.print("Enter new name (press Enter to skip): ");
        String name = scanner.nextLine().trim();
        
        System.out.print("Enter new email (press Enter to skip): ");
        String email = scanner.nextLine().trim();
        
        System.out.print("Enter new phone (press Enter to skip): ");
        String phone = scanner.nextLine().trim();
        
        UpdateUserRequest request = new UpdateUserRequest(
            name.isEmpty() ? null : name,
            email.isEmpty() ? null : email,
            phone.isEmpty() ? null : phone
        );
        
        if (!request.hasUpdates()) {
            System.out.println("No updates provided.");
            return;
        }
        
        UserResponse updatedUser = userService.updateUser(userId, request);
        System.out.println("User updated successfully:");
        printUser(updatedUser);
    }
    
    private void deactivateUser() {
        System.out.println("\n--- Deactivate User ---");

        System.out.print("Enter user ID: ");
        String userId = scanner.nextLine().trim();

        boolean deactivated = userService.deactivateUser(userId);
        if (deactivated) {
            System.out.println("User deactivated successfully.");
        } else {
            System.out.println("User not found with ID: " + userId);
        }
    }

    private void listAllUsers() {
        System.out.println("\n--- All Active Users ---");

        List<UserResponse> users = userService.getAllActiveUsers();
        if (users.isEmpty()) {
            System.out.println("No active users found.");
        } else {
            System.out.println("Found " + users.size() + " active user(s):");
            for (int i = 0; i < users.size(); i++) {
                System.out.println("\n" + (i + 1) + ".");
                printUser(users.get(i));
            }
        }
    }

    private void searchUsers() {
        System.out.println("\n--- Search Users by Name ---");

        System.out.print("Enter name pattern: ");
        String pattern = scanner.nextLine().trim();

        List<UserResponse> users = userService.searchUsersByName(pattern);
        if (users.isEmpty()) {
            System.out.println("No users found matching pattern: " + pattern);
        } else {
            System.out.println("Found " + users.size() + " user(s) matching '" + pattern + "':");
            for (int i = 0; i < users.size(); i++) {
                System.out.println("\n" + (i + 1) + ".");
                printUser(users.get(i));
            }
        }
    }

    private void exportUser() throws UserServiceException {
        System.out.println("\n--- Export User as JSON ---");

        System.out.print("Enter user ID: ");
        String userId = scanner.nextLine().trim();

        String json = userService.exportUserAsJson(userId);
        System.out.println("User JSON export:");
        System.out.println(json);
    }

    private void demonstrateDataProcessors() {
        System.out.println("\n--- Data Processors Demonstration ---");
        System.out.println("This demonstrates the Service Provider Interface (SPI) pattern.");
        System.out.println("Different data processors handle different types of data:");
        System.out.println();

        // Demonstrate different data processing scenarios
        String[] testData = {
            "john doe",                    // Regular text - handled by default processor
            "json test data",              // Contains 'json' - handled by JSON processor
            "{\"name\": \"test\"}",        // JSON format - handled by JSON processor
            "xml test data",               // Contains 'xml' - handled by XML processor
            "<user>test</user>",           // XML format - handled by XML processor
            "regular text"                 // Regular text - handled by default processor
        };

        for (String data : testData) {
            try {
                System.out.println("Input: " + data);

                // Create a temporary user to see how the data is processed
                CreateUserRequest request = new CreateUserRequest(data, "<EMAIL>", null);
                UserResponse user = userService.createUser(request);

                System.out.println("Processed name: " + user.getName());
                System.out.println("---");

                // Clean up - deactivate the test user
                userService.deactivateUser(user.getId());

            } catch (UserServiceException e) {
                System.out.println("Error processing '" + data + "': " + e.getMessage());
                System.out.println("---");
            }
        }

        System.out.println("Data processing demonstration completed.");
        System.out.println("Notice how different processors handled different data types!");
    }

    private void printUser(UserResponse user) {
        System.out.println("  ID: " + user.getId());
        System.out.println("  Name: " + user.getName());
        System.out.println("  Email: " + user.getEmail());
        System.out.println("  Phone: " + (user.getPhone() != null ? user.getPhone() : "N/A"));
        System.out.println("  Created: " + user.getCreatedAt());
        System.out.println("  Active: " + user.isActive());
    }
}
