#!/bin/bash

# Java Modules Demo - 运行脚本
# 这个脚本提供了多种方式来构建和运行 Java 模块化示例项目

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Java 版本
check_java_version() {
    print_info "检查 Java 版本..."
    
    if ! command -v java &> /dev/null; then
        print_error "Java 未安装或不在 PATH 中"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$JAVA_VERSION" -lt 21 ]; then
        print_error "需要 Java 21 或更高版本，当前版本: $JAVA_VERSION"
        exit 1
    fi
    
    print_success "Java 版本检查通过: $(java -version 2>&1 | head -n1)"
}

# 检查 Maven 版本
check_maven_version() {
    print_info "检查 Maven 版本..."
    
    if ! command -v mvn &> /dev/null; then
        print_error "Maven 未安装或不在 PATH 中"
        exit 1
    fi
    
    print_success "Maven 版本: $(mvn -version | head -n1)"
}

# 清理项目
clean_project() {
    print_info "清理项目..."
    mvn clean
    rm -rf target/modules
    rm -rf logs
    print_success "项目清理完成"
}

# 编译项目
compile_project() {
    print_info "编译项目..."
    mvn compile
    print_success "项目编译完成"
}

# 运行测试
run_tests() {
    print_info "运行单元测试..."
    mvn test
    print_success "单元测试完成"
}

# 运行集成测试
run_integration_tests() {
    print_info "运行集成测试..."
    mvn verify
    print_success "集成测试完成"
}

# 打包项目
package_project() {
    print_info "打包项目..."
    mvn package
    
    # 创建模块目录
    mkdir -p target/modules
    
    # 复制所有模块 JAR 到统一目录
    find . -name "*.jar" -path "*/target/*" -not -path "*/integration-tests/*" -exec cp {} target/modules/ \;
    
    # 复制依赖 JAR
    mvn dependency:copy-dependencies -DoutputDirectory=target/modules -DincludeScope=runtime
    
    print_success "项目打包完成，模块 JAR 位于 target/modules/"
}

# 运行应用程序
run_application() {
    print_info "运行客户端应用程序..."
    
    # 确保项目已编译
    if [ ! -d "target/modules" ]; then
        print_warning "项目未打包，正在打包..."
        package_project
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 运行应用程序
    java --module-path target/modules \
         --module com.example.jpms.client/com.example.jpms.client.ClientApplication
}

# 运行应用程序（开发模式）
run_application_dev() {
    print_info "运行客户端应用程序（开发模式）..."
    
    # 使用 Maven exec 插件运行
    mvn -pl client-app exec:java -Dexec.mainClass="com.example.jpms.client.ClientApplication"
}

# 显示模块信息
show_module_info() {
    print_info "显示模块信息..."
    
    if [ ! -d "target/modules" ]; then
        print_warning "项目未打包，正在打包..."
        package_project
    fi
    
    echo
    print_info "=== 模块列表 ==="
    for jar in target/modules/*.jar; do
        if [[ $jar == *"core-utils"* ]] || [[ $jar == *"user-service"* ]] || [[ $jar == *"client-app"* ]]; then
            echo "  $(basename "$jar")"
            java --module-path target/modules --describe-module $(basename "$jar" .jar) 2>/dev/null || true
            echo
        fi
    done
}

# 运行特定测试
run_specific_test() {
    local test_class=$1
    if [ -z "$test_class" ]; then
        print_error "请指定测试类名"
        exit 1
    fi
    
    print_info "运行测试: $test_class"
    mvn -pl integration-tests test -Dtest="$test_class"
}

# 显示帮助信息
show_help() {
    echo "Java Modules Demo - 运行脚本"
    echo
    echo "用法: $0 [命令]"
    echo
    echo "可用命令:"
    echo "  check       - 检查环境（Java 和 Maven 版本）"
    echo "  clean       - 清理项目"
    echo "  compile     - 编译项目"
    echo "  test        - 运行单元测试"
    echo "  integration - 运行集成测试"
    echo "  package     - 打包项目"
    echo "  run         - 运行客户端应用程序"
    echo "  run-dev     - 运行客户端应用程序（开发模式）"
    echo "  modules     - 显示模块信息"
    echo "  test-class <类名> - 运行特定测试类"
    echo "  all         - 执行完整的构建和测试流程"
    echo "  help        - 显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 all                           # 完整构建和测试"
    echo "  $0 run                           # 运行应用程序"
    echo "  $0 test-class ModuleIntegrationTest  # 运行特定测试"
}

# 执行完整流程
run_all() {
    print_info "执行完整的构建和测试流程..."
    
    check_java_version
    check_maven_version
    clean_project
    compile_project
    run_tests
    run_integration_tests
    package_project
    show_module_info
    
    print_success "完整流程执行完成！"
    print_info "现在可以运行: $0 run"
}

# 主函数
main() {
    case "${1:-help}" in
        "check")
            check_java_version
            check_maven_version
            ;;
        "clean")
            clean_project
            ;;
        "compile")
            compile_project
            ;;
        "test")
            run_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "package")
            package_project
            ;;
        "run")
            run_application
            ;;
        "run-dev")
            run_application_dev
            ;;
        "modules")
            show_module_info
            ;;
        "test-class")
            run_specific_test "$2"
            ;;
        "all")
            run_all
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
