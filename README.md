# Java 模块化示例项目 (JPMS Demo)

这是一个全面的 Java 平台模块系统 (JPMS) 示例项目，使用 JDK 21 构建，旨在帮助开发者理解和掌握 Java 模块化的核心概念和最佳实践。

## 🎯 项目目标

本项目通过实际的代码示例展示以下 JPMS 特性：

- **模块封装**：展示哪些代码可以被其他模块访问，哪些被隐藏
- **模块依赖**：演示模块间的依赖关系和传递依赖
- **服务提供者接口 (SPI)**：展示服务加载机制和多实现
- **与外部库的集成**：演示如何在模块化项目中使用 Jackson、SLF4J 等库
- **反射访问控制**：展示 `opens` 指令的使用场景
- **测试策略**：演示跨模块测试的配置和实现

## 📁 项目结构

```
java-modules-demo/
├── core-utils/              # 核心工具模块
│   ├── src/main/java/
│   │   ├── module-info.java
│   │   └── com/example/jpms/core/
│   │       ├── util/        # 导出的工具类
│   │       ├── model/       # 导出的模型类
│   │       ├── spi/         # 导出的服务接口
│   │       └── internal/    # 内部实现（不导出）
│   └── src/test/java/       # 单元测试
├── user-service/           # 用户服务模块
│   ├── src/main/java/
│   │   ├── module-info.java
│   │   └── com/example/jpms/userservice/
│   │       ├── api/         # 导出的服务 API
│   │       ├── dto/         # 导出的数据传输对象
│   │       └── internal/    # 内部实现（不导出）
│   └── src/test/java/       # 单元测试
├── client-app/             # 客户端应用模块
│   ├── src/main/java/
│   │   ├── module-info.java
│   │   └── com/example/jpms/client/
│   │       └── ClientApplication.java  # 主应用程序
│   └── src/main/resources/
│       └── logback.xml      # 日志配置
├── integration-tests/      # 集成测试模块
│   └── src/test/java/
│       ├── module-info.java
│       └── com/example/jpms/integration/tests/
└── pom.xml                 # 根 Maven 配置
```

## 🔧 模块详细说明

### 1. core-utils 模块

**模块名称**: `com.example.jpms.core`

**主要功能**:
- 提供通用工具类（字符串处理、验证等）
- 定义共享的数据模型
- 提供服务提供者接口 (SPI)
- 包含默认的服务实现

**关键特性**:
```java
module com.example.jpms.core {
    // 外部依赖
    requires org.slf4j;
    requires com.fasterxml.jackson.core;
    requires com.fasterxml.jackson.databind;
    
    // 导出公共 API 包
    exports com.example.jpms.core.util;
    exports com.example.jpms.core.model;
    exports com.example.jpms.core.spi;
    
    // 传递依赖
    requires transitive org.slf4j;
    
    // 为 Jackson 序列化开放包
    opens com.example.jpms.core.model to com.fasterxml.jackson.databind;
    
    // SPI 声明
    uses com.example.jpms.core.spi.DataProcessor;
    provides com.example.jpms.core.spi.DataProcessor 
        with com.example.jpms.core.internal.DefaultDataProcessor;
}
```

### 2. user-service 模块

**模块名称**: `com.example.jpms.userservice`

**主要功能**:
- 用户管理服务实现
- 依赖核心模块的工具和模型
- 提供额外的数据处理器实现
- 展示模块间依赖和服务扩展

**关键特性**:
```java
module com.example.jpms.userservice {
    // 依赖核心模块
    requires com.example.jpms.core;
    requires com.fasterxml.jackson.core;
    requires com.fasterxml.jackson.databind;
    
    // 导出服务 API
    exports com.example.jpms.userservice.api;
    exports com.example.jpms.userservice.dto;
    
    // 为 Jackson 开放 DTO 包
    opens com.example.jpms.userservice.dto to com.fasterxml.jackson.databind;
    
    // 使用和提供服务
    uses com.example.jpms.core.spi.DataProcessor;
    provides com.example.jpms.core.spi.DataProcessor 
        with com.example.jpms.userservice.internal.JsonDataProcessor,
             com.example.jpms.userservice.internal.XmlDataProcessor;
}
```

### 3. client-app 模块

**模块名称**: `com.example.jpms.client`

**主要功能**:
- 交互式控制台应用程序
- 演示完整的模块依赖链
- 展示服务消费和错误处理
- 提供用户友好的界面来测试所有功能

### 4. integration-tests 模块

**模块名称**: `com.example.jpms.integration.tests`

**主要功能**:
- 跨模块集成测试
- 服务加载机制测试
- 模块封装验证
- 端到端功能测试

## 🚀 快速开始

### 前置要求

- JDK 21 或更高版本
- Maven 3.8.0 或更高版本

### 编译项目

```bash
# 编译所有模块
mvn clean compile

# 运行测试
mvn test

# 运行集成测试
mvn verify
```

### 运行应用程序

```bash
# 方法 1: 使用 Maven
mvn -pl client-app exec:java -Dexec.mainClass="com.example.jpms.client.ClientApplication"

# 方法 2: 使用编译后的类文件
java --module-path target/modules --module com.example.jpms.client/com.example.jpms.client.ClientApplication
```

### 创建模块化 JAR

```bash
# 打包所有模块
mvn clean package

# 运行打包后的应用
java --module-path target/modules --module com.example.jpms.client
```

## 📚 关键概念演示

### 1. 模块封装

项目演示了如何使用模块系统控制 API 的可见性：

- **导出的包** (`exports`): 其他模块可以访问
- **内部包**: 只能在模块内部访问
- **开放的包** (`opens`): 允许反射访问（如 Jackson 序列化）

### 2. 服务提供者接口 (SPI)

项目实现了一个完整的 SPI 示例：

```java
// 定义服务接口
public interface DataProcessor {
    String process(String data);
    boolean canProcess(String data);
    int getPriority();
}

// 在 module-info.java 中声明
uses com.example.jpms.core.spi.DataProcessor;
provides com.example.jpms.core.spi.DataProcessor 
    with com.example.jpms.core.internal.DefaultDataProcessor;
```

### 3. 传递依赖

演示了如何使用 `requires transitive` 来暴露传递依赖：

```java
// core-utils 模块
requires transitive org.slf4j;

// user-service 模块可以直接使用 SLF4J，无需显式声明依赖
```

### 4. 反射访问控制

展示了如何为需要反射的库（如 Jackson）开放包：

```java
opens com.example.jpms.core.model to com.fasterxml.jackson.databind;
```

## 🧪 测试策略

### 单元测试
每个模块都包含单元测试，测试模块内部的功能。

### 集成测试
`integration-tests` 模块包含跨模块的集成测试：

- **ModuleIntegrationTest**: 测试模块间的协作
- **ServiceLoadingTest**: 测试 SPI 机制

### 运行测试

```bash
# 运行所有测试
mvn test

# 只运行集成测试
mvn -pl integration-tests test

# 运行特定测试类
mvn -pl integration-tests test -Dtest=ModuleIntegrationTest
```

## 🔍 常见问题和解决方案

### 1. 模块路径 vs 类路径

**问题**: 理解何时使用模块路径，何时使用类路径。

**解决方案**: 
- 模块化的 JAR 应该放在模块路径上
- 非模块化的 JAR 可以放在类路径上，但会被视为"自动模块"

### 2. 反射访问问题

**问题**: 库需要反射访问但被模块系统阻止。

**解决方案**: 使用 `opens` 指令开放特定包给特定模块：
```java
opens com.example.package to library.module;
```

### 3. 服务加载失败

**问题**: ServiceLoader 找不到服务实现。

**解决方案**: 确保在 `module-info.java` 中正确声明 `provides` 和 `uses`。

### 4. 循环依赖

**问题**: 模块间出现循环依赖。

**解决方案**: 重新设计模块结构，提取共同依赖到单独的模块。

## 🛠️ 开发工具支持

### IDE 配置

**IntelliJ IDEA**:
- 确保项目 SDK 设置为 JDK 21
- 启用模块支持
- 配置模块路径而不是类路径

**Eclipse**:
- 安装最新版本以支持 JPMS
- 配置项目使用模块路径

### Maven 配置要点

```xml
<properties>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <maven.compiler.release>21</maven.compiler.release>
</properties>
```

## 📈 扩展建议

1. **添加更多服务实现**: 创建新的 DataProcessor 实现
2. **集成 Spring Framework**: 演示模块化 Spring 应用
3. **微服务架构**: 将模块拆分为独立的微服务
4. **性能监控**: 添加模块级别的性能监控
5. **安全性**: 演示模块级别的安全控制

## 📄 许可证

本项目仅用于教育和演示目的。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个示例项目。

---

**注意**: 这个项目专注于演示 JPMS 的核心概念。在生产环境中使用时，请考虑额外的安全性、性能和可维护性要求。
