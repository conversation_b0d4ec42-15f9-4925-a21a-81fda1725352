<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.example.jpms</groupId>
        <artifactId>java-modules-demo</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>integration-tests</artifactId>
    <name>Integration Tests Module</name>
    <description>Integration tests demonstrating cross-module testing scenarios</description>

    <dependencies>
        <!-- Dependencies on all modules for integration testing -->
        <dependency>
            <groupId>com.example.jpms</groupId>
            <artifactId>core-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.example.jpms</groupId>
            <artifactId>user-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.example.jpms</groupId>
            <artifactId>client-app</artifactId>
        </dependency>
        
        <!-- Logback for logging -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <!-- Test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
