/**
 * Integration tests module for cross-module testing scenarios.
 * 
 * This module demonstrates:
 * - Testing across multiple modules
 * - Module configuration for test scenarios
 * - Access to internal packages for testing (using opens)
 * - Service loading testing
 * 
 * Note: This module-info.java is in src/test/java to demonstrate
 * test-specific module configuration.
 */
module com.example.jpms.integration.tests {
    // Dependencies on all modules being tested
    requires com.example.jpms.core;
    requires com.example.jpms.userservice;
    requires com.example.jpms.client;
    
    // Test framework dependencies
    requires org.junit.jupiter.api;
    requires org.junit.jupiter.engine;
    
    // Logging
    requires ch.qos.logback.classic;
    requires ch.qos.logback.core;
    
    // Open packages for testing frameworks that need reflection access
    opens com.example.jpms.integration.tests to org.junit.platform.commons;
    
    // We don't export any packages since this is a test module
}
