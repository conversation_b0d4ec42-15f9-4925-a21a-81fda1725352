package com.example.jpms.integration.tests;

import com.example.jpms.core.model.User;
import com.example.jpms.core.spi.DataProcessor;
import com.example.jpms.core.util.StringUtils;
import com.example.jpms.core.util.ValidationUtils;
import com.example.jpms.userservice.api.UserService;
import com.example.jpms.userservice.api.UserServiceException;
import com.example.jpms.userservice.api.UserServiceFactory;
import com.example.jpms.userservice.api.ValidationResult;
import com.example.jpms.userservice.dto.CreateUserRequest;
import com.example.jpms.userservice.dto.UserResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.ServiceLoader;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests demonstrating cross-module functionality.
 * 
 * This test class demonstrates:
 * - Testing across multiple modules
 * - Service loading verification
 * - Module encapsulation testing
 * - End-to-end functionality testing
 */
@DisplayName("Module Integration Tests")
class ModuleIntegrationTest {
    private static final Logger logger = LoggerFactory.getLogger(ModuleIntegrationTest.class);
    
    private UserService userService;
    
    @BeforeEach
    void setUp() {
        userService = UserServiceFactory.createInstance();
        logger.info("Set up new UserService instance for test");
    }
    
    @Test
    @DisplayName("Core utilities should be accessible from user service")
    void testCoreUtilitiesIntegration() {
        // Test that core utilities work correctly
        assertTrue(StringUtils.isEmpty(""));
        assertTrue(StringUtils.isEmpty(null));
        assertFalse(StringUtils.isEmpty("test"));
        
        assertEquals("Hello World", StringUtils.capitalize("hello world"));
        assertEquals("dlrow olleh", StringUtils.reverse("hello world"));
        
        assertTrue(ValidationUtils.isValidEmail("<EMAIL>"));
        assertFalse(ValidationUtils.isValidEmail("invalid-email"));
        
        assertTrue(ValidationUtils.isValidPhone("+**********"));
        assertFalse(ValidationUtils.isValidPhone("invalid-phone"));
        
        logger.info("Core utilities integration test passed");
    }
    
    @Test
    @DisplayName("Service Provider Interface should load all processors")
    void testServiceProviderInterfaceLoading() {
        // Load all DataProcessor implementations
        List<DataProcessor> processors = ServiceLoader.load(DataProcessor.class)
                .stream()
                .map(ServiceLoader.Provider::get)
                .collect(Collectors.toList());
        
        // Should have at least 3 processors: Default, JSON, and XML
        assertTrue(processors.size() >= 3, 
                  "Expected at least 3 data processors, found: " + processors.size());
        
        // Verify processor names
        List<String> processorNames = processors.stream()
                .map(DataProcessor::getName)
                .collect(Collectors.toList());
        
        assertTrue(processorNames.contains("Default Data Processor"));
        assertTrue(processorNames.contains("JSON Data Processor"));
        assertTrue(processorNames.contains("XML Data Processor"));
        
        logger.info("Found {} data processors: {}", processors.size(), processorNames);
    }
    
    @Test
    @DisplayName("User service should integrate with core utilities and data processors")
    void testUserServiceIntegration() throws UserServiceException {
        // Test creating a user with different data types to trigger different processors
        
        // Test 1: Regular text (should use default processor)
        CreateUserRequest request1 = new CreateUserRequest("john doe", "<EMAIL>", "+**********");
        UserResponse user1 = userService.createUser(request1);
        
        assertNotNull(user1);
        assertEquals("<EMAIL>", user1.getEmail());
        assertTrue(user1.getName().contains("John Doe")); // Should be capitalized by default processor
        
        // Test 2: JSON-like data (should use JSON processor)
        CreateUserRequest request2 = new CreateUserRequest("json test user", "<EMAIL>", null);
        UserResponse user2 = userService.createUser(request2);
        
        assertNotNull(user2);
        assertTrue(user2.getName().contains("JSON") || user2.getName().contains("json"));
        
        // Test 3: XML-like data (should use XML processor)
        CreateUserRequest request3 = new CreateUserRequest("xml test user", "<EMAIL>", null);
        UserResponse user3 = userService.createUser(request3);
        
        assertNotNull(user3);
        assertTrue(user3.getName().contains("XML") || user3.getName().contains("xml"));
        
        logger.info("User service integration test passed");
    }
    
    @Test
    @DisplayName("User validation should work across modules")
    void testCrossModuleValidation() {
        // Test validation using core utilities through user service
        User validUser = new User("1", "John Doe", "<EMAIL>", "+**********");
        ValidationResult result = userService.validateUser(validUser);
        
        assertTrue(result.isValid());
        assertTrue(result.getErrors().isEmpty());
        
        // Test invalid user
        User invalidUser = new User("2", "", "invalid-email", "invalid-phone");
        ValidationResult invalidResult = userService.validateUser(invalidUser);
        
        assertFalse(invalidResult.isValid());
        assertFalse(invalidResult.getErrors().isEmpty());
        
        logger.info("Cross-module validation test passed");
    }
    
    @Test
    @DisplayName("JSON export should work with Jackson integration")
    void testJsonExportIntegration() throws UserServiceException {
        // Create a user
        CreateUserRequest request = new CreateUserRequest("Test User", "<EMAIL>", "+**********");
        UserResponse user = userService.createUser(request);
        
        // Export as JSON
        String json = userService.exportUserAsJson(user.getId());
        
        assertNotNull(json);
        assertTrue(json.contains("\"id\""));
        assertTrue(json.contains("\"name\""));
        assertTrue(json.contains("\"email\""));
        assertTrue(json.contains("<EMAIL>"));
        
        logger.info("JSON export integration test passed");
    }
    
    @Test
    @DisplayName("Module encapsulation should prevent access to internal packages")
    void testModuleEncapsulation() {
        // This test verifies that internal packages are not accessible
        // If this test compiles, it means encapsulation is working correctly
        
        // We can access exported packages
        assertDoesNotThrow(() -> {
            StringUtils.isEmpty("test");
            ValidationUtils.isValidEmail("<EMAIL>");
            UserServiceFactory.getInstance();
        });
        
        // We cannot access internal packages (this would cause compilation errors):
        // - com.example.jpms.core.internal.DefaultDataProcessor (not exported)
        // - com.example.jpms.userservice.internal.UserServiceImpl (not exported)
        
        logger.info("Module encapsulation test passed");
    }
    
    @Test
    @DisplayName("End-to-end user management workflow should work")
    void testEndToEndWorkflow() throws UserServiceException {
        // Create user
        CreateUserRequest createRequest = new CreateUserRequest("Jane Doe", "<EMAIL>", "+9876543210");
        UserResponse createdUser = userService.createUser(createRequest);
        
        assertNotNull(createdUser);
        assertNotNull(createdUser.getId());
        assertTrue(createdUser.isActive());
        
        // Retrieve user by ID
        var userById = userService.getUserById(createdUser.getId());
        assertTrue(userById.isPresent());
        assertEquals(createdUser.getId(), userById.get().getId());
        
        // Retrieve user by email
        var userByEmail = userService.getUserByEmail("<EMAIL>");
        assertTrue(userByEmail.isPresent());
        assertEquals(createdUser.getId(), userByEmail.get().getId());
        
        // List all users (should include our user)
        List<UserResponse> allUsers = userService.getAllActiveUsers();
        assertTrue(allUsers.stream().anyMatch(u -> u.getId().equals(createdUser.getId())));
        
        // Search users
        List<UserResponse> searchResults = userService.searchUsersByName("Jane");
        assertTrue(searchResults.stream().anyMatch(u -> u.getId().equals(createdUser.getId())));
        
        // Deactivate user
        boolean deactivated = userService.deactivateUser(createdUser.getId());
        assertTrue(deactivated);
        
        // Verify user is no longer in active list
        List<UserResponse> activeUsersAfterDeactivation = userService.getAllActiveUsers();
        assertFalse(activeUsersAfterDeactivation.stream().anyMatch(u -> u.getId().equals(createdUser.getId())));
        
        logger.info("End-to-end workflow test passed");
    }
}
