package com.example.jpms.integration.tests;

import com.example.jpms.core.spi.DataProcessor;
import com.example.jpms.core.spi.ProcessingException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.ServiceLoader;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Tests for Service Provider Interface (SPI) loading mechanism.
 * 
 * This test class demonstrates:
 * - ServiceLoader functionality in modules
 * - Multiple service implementations
 * - Service priority handling
 * - Service capability testing
 */
@DisplayName("Service Loading Tests")
class ServiceLoadingTest {
    private static final Logger logger = LoggerFactory.getLogger(ServiceLoadingTest.class);
    
    @Test
    @DisplayName("ServiceLoader should discover all DataProcessor implementations")
    void testServiceDiscovery() {
        List<DataProcessor> processors = ServiceLoader.load(DataProcessor.class)
                .stream()
                .map(ServiceLoader.Provider::get)
                .collect(Collectors.toList());
        
        assertFalse(processors.isEmpty(), "No DataProcessor implementations found");
        
        // Log discovered services
        logger.info("Discovered {} DataProcessor implementations:", processors.size());
        processors.forEach(processor -> 
            logger.info("  - {} v{} (priority: {})", 
                processor.getName(), processor.getVersion(), processor.getPriority()));
        
        // Verify expected implementations
        List<String> names = processors.stream()
                .map(DataProcessor::getName)
                .collect(Collectors.toList());
        
        assertTrue(names.contains("Default Data Processor"));
        assertTrue(names.contains("JSON Data Processor"));
        assertTrue(names.contains("XML Data Processor"));
    }
    
    @Test
    @DisplayName("Processors should be sorted by priority")
    void testProcessorPriority() {
        List<DataProcessor> processors = ServiceLoader.load(DataProcessor.class)
                .stream()
                .map(ServiceLoader.Provider::get)
                .sorted((p1, p2) -> Integer.compare(p2.getPriority(), p1.getPriority()))
                .collect(Collectors.toList());
        
        // Verify priority order
        for (int i = 0; i < processors.size() - 1; i++) {
            assertTrue(processors.get(i).getPriority() >= processors.get(i + 1).getPriority(),
                      "Processors should be sorted by priority (highest first)");
        }
        
        logger.info("Processor priority order verified");
    }
    
    @Test
    @DisplayName("JSON processor should handle JSON data with highest priority")
    void testJsonProcessorHandling() throws ProcessingException {
        List<DataProcessor> processors = ServiceLoader.load(DataProcessor.class)
                .stream()
                .map(ServiceLoader.Provider::get)
                .filter(p -> p.canProcess("{\"test\": \"data\"}"))
                .sorted((p1, p2) -> Integer.compare(p2.getPriority(), p1.getPriority()))
                .collect(Collectors.toList());
        
        assertFalse(processors.isEmpty(), "No processor can handle JSON data");
        
        DataProcessor topProcessor = processors.get(0);
        assertEquals("JSON Data Processor", topProcessor.getName());
        
        String result = topProcessor.process("{\"test\": \"data\"}");
        assertTrue(result.contains("JSON") || result.contains("json"));
        
        logger.info("JSON processor handling verified: {}", result);
    }
    
    @Test
    @DisplayName("XML processor should handle XML data")
    void testXmlProcessorHandling() throws ProcessingException {
        List<DataProcessor> processors = ServiceLoader.load(DataProcessor.class)
                .stream()
                .map(ServiceLoader.Provider::get)
                .filter(p -> p.canProcess("<test>data</test>"))
                .collect(Collectors.toList());
        
        assertFalse(processors.isEmpty(), "No processor can handle XML data");
        
        DataProcessor xmlProcessor = processors.stream()
                .filter(p -> p.getName().equals("XML Data Processor"))
                .findFirst()
                .orElseThrow(() -> new AssertionError("XML processor not found"));
        
        String result = xmlProcessor.process("<test>data</test>");
        assertTrue(result.contains("XML") || result.contains("xml"));
        
        logger.info("XML processor handling verified: {}", result);
    }
    
    @Test
    @DisplayName("Default processor should handle any data")
    void testDefaultProcessorHandling() throws ProcessingException {
        DataProcessor defaultProcessor = ServiceLoader.load(DataProcessor.class)
                .stream()
                .map(ServiceLoader.Provider::get)
                .filter(p -> p.getName().equals("Default Data Processor"))
                .findFirst()
                .orElseThrow(() -> new AssertionError("Default processor not found"));
        
        // Default processor should handle any non-null data
        assertTrue(defaultProcessor.canProcess("any text"));
        assertTrue(defaultProcessor.canProcess("123"));
        assertTrue(defaultProcessor.canProcess("special chars !@#$%"));
        assertFalse(defaultProcessor.canProcess(null));
        
        String result = defaultProcessor.process("test data");
        assertTrue(result.contains("PROCESSED"));
        assertTrue(result.contains("Test Data")); // Should be capitalized
        
        logger.info("Default processor handling verified: {}", result);
    }
    
    @Test
    @DisplayName("Service loading should work with module system")
    void testModuleSystemIntegration() {
        // Verify that ServiceLoader works correctly in the module system
        ServiceLoader<DataProcessor> serviceLoader = ServiceLoader.load(DataProcessor.class);
        
        assertNotNull(serviceLoader);
        
        // Test that we can iterate over services
        int count = 0;
        for (DataProcessor processor : serviceLoader) {
            assertNotNull(processor);
            assertNotNull(processor.getName());
            assertNotNull(processor.getVersion());
            count++;
        }
        
        assertTrue(count > 0, "ServiceLoader should find at least one implementation");
        
        // Test that we can reload services
        serviceLoader.reload();
        
        long reloadedCount = serviceLoader.stream().count();
        assertEquals(count, reloadedCount, "Reload should find the same number of services");
        
        logger.info("Module system integration verified with {} services", count);
    }
    
    @Test
    @DisplayName("Processor selection should work based on data type")
    void testProcessorSelection() throws ProcessingException {
        String[] testCases = {
            "regular text",
            "json data",
            "{\"key\": \"value\"}",
            "xml data",
            "<root>content</root>"
        };
        
        for (String testData : testCases) {
            // Find the best processor for this data
            DataProcessor bestProcessor = ServiceLoader.load(DataProcessor.class)
                    .stream()
                    .map(ServiceLoader.Provider::get)
                    .filter(p -> p.canProcess(testData))
                    .max((p1, p2) -> Integer.compare(p1.getPriority(), p2.getPriority()))
                    .orElseThrow(() -> new AssertionError("No processor found for: " + testData));
            
            String result = bestProcessor.process(testData);
            assertNotNull(result);
            assertFalse(result.isEmpty());
            
            logger.info("Data '{}' processed by {} -> '{}'", 
                       testData, bestProcessor.getName(), result);
        }
    }
}
